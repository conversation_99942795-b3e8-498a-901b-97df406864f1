defmodule Users do
  use Drops.Relation, repo: Drops.Relation.Repos.Postgres

  schema("users", infer: true)

  defquery active() do
    from(u in relation(), where: u.active == true)
  end
end

Enum.each(Users.all(), &Users.delete/1)

Users.insert(%{name: "<PERSON>", active: false})
Users.insert(%{name: "<PERSON>", active: true})
Users.insert(%{name: "<PERSON>", active: false})
Users.insert(%{name: "<PERSON>", active: true})

IO.inspect(Users.active() |> Enum.to_list())
# [
#   %Users.Schemas.Active{
#     __meta__: #Ecto.Schema.Metadata<:loaded, "users">,
#     id: 2,
#     name: "<PERSON>",
#     active: true
#   },
#   %Users.Schemas.Active{
#     __meta__: #Ecto.Schema.Metadata<:loaded, "users">,
#     id: 4,
#     name: "<PERSON>",
#     active: true
#   }
# ]
